<template>
  <!-- 冲销附件 -->
  <div class="detail-table">
    <ScTable
      ref="scTableRef"
      :columns="columns"
      :table-data="tableData"
      height="180"
      :is-show-right-btn="false"
      :auto-height="false"
      :loading="loading"
      :row-config="{ height: 45 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'
import { download } from '@/utils/utils'

export default {
  components: { ScTable },
  mixins: [mixin],
  data() {
    return {
      type: 'writeOffAttachment'
    }
  },
  computed: {
    dataList() {
      const dataList = []
      this.tableData.forEach((item) => {
        item.id = this.$route.query.id || null
        dataList.push(item)
      })
      return dataList
    }
  },
  methods: {
    // 点击工具栏按钮
    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete'].includes(item.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (item.code) {
        case 'upload':
          this.handleUpload()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'download':
          this.handleDownload(selectedRecords)
          break
        default:
          break
      }
    },
    // 上传
    handleUpload() {
      this.$dialog({
        modal: () => import('./uploadDialog.vue'),
        data: {
          title: this.$t('上传冲销附件'),
          uploadUrl: '/analysis/tenant/rebateHeader/uploadWriteOffFile',
          uploadData: {
            id: this.$route.query.id
          }
        },
        success: () => {
          this.$emit('refresh')
        }
      })
    },
    // 删除
    async handleDelete(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除?')
        },
        success: async () => {
          const idList = selectedRecords.map((item) => item.id)
          const res = await this.$API.rebateManagement.deleteWriteOffAttachment(idList)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.$emit('refresh')
          }
        }
      })
    },
    // 下载
    async handleDownload(selectedRecords) {
      if (!selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      for (const item of selectedRecords) {
        try {
          const res = await this.$API.rebateManagement.downloadWriteOffAttachment({
            id: item.id
          })
          if (res.data) {
            download({ fileName: item.attachmentName, blob: res.data })
          }
        } catch (error) {
          console.error('下载失败:', error)
          this.$toast({ content: this.$t('下载失败'), type: 'error' })
        }
      }
    },
    // 点击单元格标题
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'attachmentName':
          this.handleDownload([row])
          break
        default:
          break
      }
    },
    // 点击单元格工具
    handleClickCellTool(code, row) {
      switch (code) {
        case 'download':
          this.handleDownload([row])
          break
        case 'delete':
          this.handleDelete([row])
          break
        default:
          break
      }
    }
  }
}
</script>

<style scoped lang="scss">
.detail-table {
  .cell-btn {
    margin-left: 8px;
    color: #409eff;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      text-decoration: underline;
    }

    i {
      margin-right: 4px;
    }
  }
}
</style>
